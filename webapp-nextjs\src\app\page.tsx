'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'

export default function Home() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading) {
      if (!isAuthenticated) {
        // Se non autenticato, reindirizza al login
        router.replace('/login')
      }
      // Se autenticato, rimani sulla homepage - non reindirizzare automaticamente
    }
  }, [isAuthenticated, isLoading, router])

  // Mostra un indicatore di caricamento durante il reindirizzamento (come nel React originale)
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-slate-900 mb-4">
          Benvenuto nel Sistema di Gestione Cantieri
        </h1>
        <p className="text-slate-600 mb-6">
          Reindirizzamento in corso...
        </p>
        <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto"></div>
      </div>
    </div>
  )
}
