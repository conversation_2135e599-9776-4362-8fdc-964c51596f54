'use client'

import { useState, useEffect, useRef } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/AuthContext'
import { Cable, User, Building2, AlertCircle, Loader2, Shield, Mail, Lock, Eye, EyeOff } from 'lucide-react'
import { validateUsername, checkRateLimit } from '@/utils/securityValidation'
import { useSecurityMonitoring } from '@/hooks/useSecurityMonitoring'

export default function LoginPage() {
  const [loginType, setLoginType] = useState<'user' | 'cantiere'>('user')
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    codice_cantiere: '',
    password_cantiere: ''
  })
  const [error, setError] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})
  const [showPassword, setShowPassword] = useState(false)
  const [showPasswordCantiere, setShowPasswordCantiere] = useState(false)
  const [fieldErrors, setFieldErrors] = useState<Record<string, boolean>>({})

  // Ref per il focus automatico sui campi con errore
  const usernameRef = useRef<HTMLInputElement>(null)
  const passwordRef = useRef<HTMLInputElement>(null)
  const codiceCantieereRef = useRef<HTMLInputElement>(null)
  const passwordCantiereRef = useRef<HTMLInputElement>(null)

  const { login, loginCantiere } = useAuth()
  const router = useRouter()
  const { logLoginAttempt, logSuspiciousActivity, getThreatLevel } = useSecurityMonitoring()

  // Focus automatico sul primo campo con errore
  useEffect(() => {
    if (Object.keys(fieldErrors).length > 0) {
      const firstErrorField = Object.keys(fieldErrors).find(field => fieldErrors[field])
      if (firstErrorField) {
        const refs = {
          username: usernameRef,
          password: passwordRef,
          codice_cantiere: codiceCantieereRef,
          password_cantiere: passwordCantiereRef
        }
        refs[firstErrorField as keyof typeof refs]?.current?.focus()
      }
    }
  }, [fieldErrors])

  // Verifica se il form è valido per abilitare il pulsante
  const isFormValid = (): boolean => {
    if (loginType === 'user') {
      return formData.username.trim() !== '' && formData.password.trim() !== ''
    } else {
      return formData.codice_cantiere.trim() !== '' && formData.password_cantiere.trim() !== ''
    }
  }

  // Validazione sicura del form
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {}

    if (loginType === 'user') {
      // Validazione semplice per login (non registrazione)
      if (!formData.username || formData.username.trim().length === 0) {
        errors.username = 'Username è obbligatorio'
      }

      // Valida password
      if (!formData.password) {
        errors.password = 'Password è obbligatoria'
      }
    } else {
      // Valida codice cantiere
      if (!formData.codice_cantiere.trim()) {
        errors.codice_cantiere = 'Codice cantiere è obbligatorio'
      } else if (formData.codice_cantiere.length < 3) {
        errors.codice_cantiere = 'Codice cantiere troppo corto'
      }

      // Valida password cantiere
      if (!formData.password_cantiere) {
        errors.password_cantiere = 'Password cantiere è obbligatoria'
      }
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    console.log('🚀 LoginPage: handleSubmit chiamato!')
    console.log('📝 LoginPage: Dati form:', { formData, loginType })

    e.preventDefault()
    setError('')
    setValidationErrors({})

    // Rate limiting per prevenire brute force
    const rateLimitKey = loginType === 'user' ? formData.username : formData.codice_cantiere
    console.log('🔒 LoginPage: Controllo rate limiting per:', rateLimitKey)
    if (!checkRateLimit(`login-${rateLimitKey}`, 5, 300000)) { // 5 tentativi per 5 minuti
      console.log('❌ LoginPage: Rate limit superato per:', rateLimitKey)
      setError('Troppi tentativi di login. Riprova tra 5 minuti.')
      logSuspiciousActivity('rate_limit_exceeded', { loginType, identifier: rateLimitKey })
      return
    }
    console.log('✅ LoginPage: Rate limiting OK')

    // Validazione form
    console.log('📋 LoginPage: Controllo validazione form')
    if (!validateForm()) {
      console.log('❌ LoginPage: Validazione form fallita')
      return
    }
    console.log('✅ LoginPage: Validazione form OK')

    // Controlla livello di minaccia
    console.log('🛡️ LoginPage: Controllo livello di minaccia')
    const threatLevel = getThreatLevel()
    if (threatLevel === 'critical') {
      console.log('❌ LoginPage: Livello di minaccia critico:', threatLevel)
      setError('Sistema temporaneamente non disponibile per motivi di sicurezza.')
      logSuspiciousActivity('login_blocked_threat_level', { threatLevel })
      return
    }
    console.log('✅ LoginPage: Livello di minaccia OK:', threatLevel)

    setIsLoading(true)

    try {
      if (loginType === 'user') {
        console.log('🚀 LoginPage: Inizio login per utente:', formData.username)
        const userData = await login(formData.username, formData.password)
        console.log('✅ LoginPage: Login completato, dati ricevuti:', userData)

        // Log tentativo di login
        logLoginAttempt(formData.username, true, { ruolo: userData?.ruolo })

        // Reindirizza in base al ruolo
        console.log('🎯 LoginPage: Reindirizzamento basato su ruolo:', userData?.ruolo)
        if (userData?.ruolo === 'owner') {
          console.log('👑 LoginPage: Reindirizzamento admin -> /admin')
          router.push('/admin')
        } else if (userData?.ruolo === 'user') {
          console.log('👤 LoginPage: Reindirizzamento user -> /cantieri')
          console.log('🔄 LoginPage: Chiamando router.push("/cantieri")')
          // Proviamo prima con window.location per debug
          console.log('🔄 LoginPage: Usando window.location.href per test')
          window.location.href = '/cantieri'
          console.log('✅ LoginPage: window.location.href="/cantieri" chiamato')
        } else if (userData?.ruolo === 'cantieri_user') {
          console.log('🏗️ LoginPage: Reindirizzamento cantiere -> /cavi')
          router.push('/cavi')
        } else {
          console.log('❓ LoginPage: Ruolo sconosciuto, default -> /cantieri')
          router.push('/cantieri')
        }
      } else {
        console.log('🏗️ LoginPage: Inizio login cantiere:', formData.codice_cantiere)
        await loginCantiere(formData.codice_cantiere, formData.password_cantiere)

        // Log tentativo di login cantiere
        logLoginAttempt(formData.codice_cantiere, true, { type: 'cantiere' })

        console.log('🎯 LoginPage: Reindirizzamento cantiere -> /cavi')
        router.push('/cavi')
      }
    } catch (error: any) {
      console.error('❌ LoginPage: Errore durante il login:', error)
      console.error('❌ LoginPage: Dettagli errore:', {
        message: error?.message,
        status: error?.status,
        response: error?.response?.data,
        stack: error?.stack
      })

      const identifier = loginType === 'user' ? formData.username : formData.codice_cantiere

      // Log tentativo fallito
      logLoginAttempt(identifier, false, {
        error: error.response?.data?.detail || error.message,
        loginType
      })

      setError(error.response?.data?.detail || 'Credenziali non valide')
    } finally {
      console.log('🏁 LoginPage: Fine processo login, setIsLoading(false)')
      setIsLoading(false)
    }
  }

  // Validazione singolo campo
  const validateField = (field: string, value: string) => {
    const errors: Record<string, boolean> = { ...fieldErrors }

    if (field === 'username' || field === 'codice_cantiere') {
      errors[field] = value.trim() === ''
    } else if (field === 'password' || field === 'password_cantiere') {
      errors[field] = value.trim() === ''
    }

    setFieldErrors(errors)
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))

    // Validazione in tempo reale solo se il campo è stato toccato
    if (fieldErrors[field] !== undefined) {
      validateField(field, value)
    }

    // Rimuovi l'errore generale se l'utente sta correggendo
    if (error && value.trim() !== '') {
      setError('')
    }
  }

  const handleFieldBlur = (field: string, value: string) => {
    validateField(field, value)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-slate-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-6">
        
        {/* Header */}
        <div className="text-center space-y-2">
          <div className="flex justify-center">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center">
              <Cable className="w-8 h-8 text-white" />
            </div>
          </div>
          <h1 className="text-2xl font-bold text-slate-900">CABLYS</h1>
          <p className="text-slate-600">Cable Installation Advance System</p>
          <p className="text-sm text-slate-700 font-medium">Sistema di gestione cavi di nuova generazione</p>
        </div>

        {/* Login Type Selector */}
        <div className="bg-slate-100 p-1 rounded-lg flex gap-1" role="tablist" aria-label="Tipo di accesso">
          <button
            type="button"
            className={`flex-1 flex items-center justify-center gap-2 px-4 py-3 rounded-md font-medium transition-all duration-200 relative focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
              loginType === 'user'
                ? 'bg-white text-slate-900 shadow-sm border-b-2 border-blue-600'
                : 'text-slate-600 hover:text-slate-800 hover:bg-slate-50'
            }`}
            onClick={() => setLoginType('user')}
            aria-pressed={loginType === 'user'}
            role="tab"
            aria-selected={loginType === 'user'}
            id="tab-user"
            aria-controls="panel-user"
          >
            <User className="w-4 h-4" aria-hidden="true" />
            Accesso Utente
            {loginType === 'user' && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-600 rounded-full" aria-hidden="true" />
            )}
          </button>
          <button
            type="button"
            className={`flex-1 flex items-center justify-center gap-2 px-4 py-3 rounded-md font-medium transition-all duration-200 relative focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 ${
              loginType === 'cantiere'
                ? 'bg-white text-slate-900 shadow-sm border-b-2 border-green-600'
                : 'text-slate-600 hover:text-slate-800 hover:bg-slate-50'
            }`}
            onClick={() => setLoginType('cantiere')}
            aria-pressed={loginType === 'cantiere'}
            role="tab"
            aria-selected={loginType === 'cantiere'}
            id="tab-cantiere"
            aria-controls="panel-cantiere"
          >
            <Building2 className="w-4 h-4" aria-hidden="true" />
            Accesso Cantiere
            {loginType === 'cantiere' && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-green-600 rounded-full" aria-hidden="true" />
            )}
          </button>
        </div>

        {/* Login Form */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {loginType === 'user' ? (
                <>
                  <User className="w-5 h-5 text-blue-600" />
                  Accesso Utente
                </>
              ) : (
                <>
                  <Building2 className="w-5 h-5 text-green-600" />
                  Accesso Cantiere
                </>
              )}
            </CardTitle>
            <CardDescription>
              {loginType === 'user' 
                ? 'Accedi con le tue credenziali utente'
                : 'Accedi con il codice cantiere'
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form
              onSubmit={handleSubmit}
              className="space-y-4"
              role="tabpanel"
              id={loginType === 'user' ? 'panel-user' : 'panel-cantiere'}
              aria-labelledby={loginType === 'user' ? 'tab-user' : 'tab-cantiere'}
              noValidate
            >
              
              {loginType === 'user' ? (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="username">Username</Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
                      <Input
                        ref={usernameRef}
                        id="username"
                        type="text"
                        placeholder="Inserisci username"
                        value={formData.username}
                        onChange={(e) => handleInputChange('username', e.target.value)}
                        onBlur={(e) => handleFieldBlur('username', e.target.value)}
                        required
                        disabled={isLoading}
                        className={`pl-10 ${fieldErrors.username ? 'border-red-500 focus-visible:border-red-500 focus-visible:ring-red-500/20' : ''}`}
                        aria-invalid={fieldErrors.username}
                        aria-describedby={fieldErrors.username ? 'username-error' : undefined}
                        autoComplete="username"
                      />
                      {fieldErrors.username && (
                        <p id="username-error" className="text-sm text-red-600 mt-1">
                          Il campo username è obbligatorio
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="password">Password</Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
                      <Input
                        ref={passwordRef}
                        id="password"
                        type={showPassword ? 'text' : 'password'}
                        placeholder="Inserisci password"
                        value={formData.password}
                        onChange={(e) => handleInputChange('password', e.target.value)}
                        onBlur={(e) => handleFieldBlur('password', e.target.value)}
                        required
                        disabled={isLoading}
                        className={`pl-10 pr-10 ${fieldErrors.password ? 'border-red-500 focus-visible:border-red-500 focus-visible:ring-red-500/20' : ''}`}
                        aria-invalid={fieldErrors.password}
                        aria-describedby={fieldErrors.password ? 'password-error' : undefined}
                        autoComplete="current-password"
                      />
                      {fieldErrors.password && (
                        <p id="password-error" className="text-sm text-red-600 mt-1">
                          Il campo password è obbligatorio
                        </p>
                      )}
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600 focus:outline-none focus:text-slate-600"
                        disabled={isLoading}
                        aria-label={showPassword ? 'Nascondi password' : 'Mostra password'}
                      >
                        {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                      </button>
                    </div>
                  </div>
                </>
              ) : (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="codice_cantiere">Codice Cantiere</Label>
                    <div className="relative">
                      <Building2 className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
                      <Input
                        ref={codiceCantieereRef}
                        id="codice_cantiere"
                        type="text"
                        placeholder="Inserisci codice cantiere"
                        value={formData.codice_cantiere}
                        onChange={(e) => handleInputChange('codice_cantiere', e.target.value)}
                        onBlur={(e) => handleFieldBlur('codice_cantiere', e.target.value)}
                        required
                        disabled={isLoading}
                        className={`pl-10 ${fieldErrors.codice_cantiere ? 'border-red-500 focus-visible:border-red-500 focus-visible:ring-red-500/20' : ''}`}
                        aria-invalid={fieldErrors.codice_cantiere}
                        aria-describedby={fieldErrors.codice_cantiere ? 'codice-cantiere-error' : undefined}
                        autoComplete="off"
                      />
                      {fieldErrors.codice_cantiere && (
                        <p id="codice-cantiere-error" className="text-sm text-red-600 mt-1">
                          Il campo codice cantiere è obbligatorio
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="password_cantiere">Password Cantiere</Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
                      <Input
                        ref={passwordCantiereRef}
                        id="password_cantiere"
                        type={showPasswordCantiere ? 'text' : 'password'}
                        placeholder="Inserisci password cantiere"
                        value={formData.password_cantiere}
                        onChange={(e) => handleInputChange('password_cantiere', e.target.value)}
                        onBlur={(e) => handleFieldBlur('password_cantiere', e.target.value)}
                        required
                        disabled={isLoading}
                        className={`pl-10 pr-10 ${fieldErrors.password_cantiere ? 'border-red-500 focus-visible:border-red-500 focus-visible:ring-red-500/20' : ''}`}
                        aria-invalid={fieldErrors.password_cantiere}
                        aria-describedby={fieldErrors.password_cantiere ? 'password-cantiere-error' : undefined}
                        autoComplete="current-password"
                      />
                      {fieldErrors.password_cantiere && (
                        <p id="password-cantiere-error" className="text-sm text-red-600 mt-1">
                          Il campo password cantiere è obbligatorio
                        </p>
                      )}
                      <button
                        type="button"
                        onClick={() => setShowPasswordCantiere(!showPasswordCantiere)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600 focus:outline-none focus:text-slate-600"
                        disabled={isLoading}
                        aria-label={showPasswordCantiere ? 'Nascondi password' : 'Mostra password'}
                      >
                        {showPasswordCantiere ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                      </button>
                    </div>
                  </div>
                </>
              )}

              {error && (
                <div className="flex items-center gap-2 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm">
                  <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0" />
                  <span className="text-sm text-red-800 font-medium">{error}</span>
                </div>
              )}

              <Button
                type="submit"
                className={`w-full transition-all duration-200 ${
                  !isFormValid() && !isLoading
                    ? 'opacity-50 cursor-not-allowed bg-slate-300 hover:bg-slate-300 text-slate-500'
                    : 'hover:shadow-md active:scale-[0.98] focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
                }`}
                disabled={isLoading || !isFormValid()}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Accesso in corso...
                  </>
                ) : (
                  'Accedi'
                )}
              </Button>

              {/* Link Password Dimenticata */}
              {loginType === 'user' && (
                <div className="text-center">
                  <button
                    type="button"
                    className="text-sm text-blue-700 hover:text-blue-900 hover:underline focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded px-1 py-1 transition-colors duration-200"
                    onClick={() => router.push('/forgot-password')}
                    aria-label="Recupera password dimenticata"
                  >
                    Password dimenticata?
                  </button>
                </div>
              )}
            </form>
          </CardContent>
        </Card>

        {/* Footer discreto con informazioni tecniche */}
        <div className="text-center">
          <div className="flex justify-center gap-2">
            <span className="text-xs text-slate-400 bg-slate-50 px-2 py-1 rounded">Next.js 15</span>
            <span className="text-xs text-slate-400 bg-slate-50 px-2 py-1 rounded">PWA Ready</span>
          </div>
        </div>

      </div>
    </div>
  )
}
