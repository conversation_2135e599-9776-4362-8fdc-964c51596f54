'use client'

import { useState, useMemo, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { getCavoColorClasses } from '@/utils/softColors'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { TableRow, TableCell } from '@/components/ui/table'
import { Cavo } from '@/types'
import FilterableTable, { ColumnDef } from '@/components/common/FilterableTable'
import SmartCaviFilter from './SmartCaviFilter'
import TruncatedText from '@/components/common/TruncatedText'
import { ActionTooltip } from './tooltips/CableTooltips'
import {
  MoreHorizontal,
  Cable,
  Settings,
  Zap,
  CheckCircle,
  AlertCircle,
  Clock,
  Package,
  Link,
  Unlink,
  Award,
  Play,
  Pause,
  X,
  Check,
  FileText,
  Download,
  AlertTriangle,
  Wrench
} from 'lucide-react'

interface CaviTableProps {
  cavi: Cavo[]
  loading?: boolean
  selectionEnabled?: boolean
  selectedCavi?: string[]
  onSelectionChange?: (selectedIds: string[]) => void
  onStatusAction?: (cavo: Cavo, action: string) => void
  onContextMenuAction?: (cavo: Cavo, action: string) => void
}

export default function CaviTable({
  cavi = [],
  loading = false,
  selectionEnabled = false,
  selectedCavi = [],
  onSelectionChange,
  onStatusAction,
  onContextMenuAction
}: CaviTableProps) {
  const [smartFilteredCavi, setSmartFilteredCavi] = useState(cavi)
  const [filteredCavi, setFilteredCavi] = useState(cavi)
  const [internalSelectionEnabled, setInternalSelectionEnabled] = useState(selectionEnabled)

  // Aggiorna i cavi quando cambiano i cavi originali
  useEffect(() => {
    setSmartFilteredCavi(cavi)
    setFilteredCavi(cavi)
  }, [cavi])

  // Gestione filtri intelligenti
  const handleSmartFilterChange = (filtered: Cavo[]) => {
    setSmartFilteredCavi(filtered)
  }

  // Gestione filtri tabella
  const handleTableFilterChange = (filtered: Cavo[]) => {
    setFilteredCavi(filtered)
  }

  const handleSelectionToggle = () => {
    setInternalSelectionEnabled(!internalSelectionEnabled)
  }

  // Gestione selezione
  const handleSelectAll = (checked: boolean) => {
    if (onSelectionChange) {
      onSelectionChange(checked ? filteredCavi.map(c => c.id_cavo) : [])
    }
  }

  const handleSelectCavo = (cavoId: string, checked: boolean) => {
    if (onSelectionChange) {
      const newSelection = checked
        ? [...selectedCavi, cavoId]
        : selectedCavi.filter(id => id !== cavoId)
      onSelectionChange(newSelection)
    }
  }

  // Bulk action handlers
  const handleBulkExport = async () => {
    try {
      const response = await fetch('/api/cavi/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          selectedIds: selectedCavi,
          cantiereId: 1 // TODO: Get from context
        })
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `cavi_export_${new Date().toISOString().split('T')[0]}.csv`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } else {
        const error = await response.json()
        alert(`Errore durante l'esportazione: ${error.error}`)
      }
    } catch (error) {
      alert('Errore durante l\'esportazione')
    }
  }

  const handleBulkStatusChange = async () => {
    const newStatus = prompt('Inserisci il nuovo stato (Da installare, In corso, Installato):')
    if (!newStatus) return

    try {
      const response = await fetch('/api/cavi/bulk-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          selectedIds: selectedCavi,
          cantiereId: 1, // TODO: Get from context
          newStatus
        })
      })

      const result = await response.json()
      if (result.success) {
        alert(result.message)
        // TODO: Refresh data
      } else {
        alert(`Errore: ${result.error}`)
      }
    } catch (error) {
      alert('Errore durante il cambio stato')
    }
  }

  const handleBulkAssignCommand = () => {
    // TODO: Implementare modal per selezione comanda
    alert(`Assegnazione comanda per ${selectedCavi.length} cavi`)
  }

  const handleBulkDelete = async () => {
    if (!confirm(`Sei sicuro di voler eliminare ${selectedCavi.length} cavi?`)) {
      return
    }

    try {
      const response = await fetch('/api/cavi/bulk-delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          selectedIds: selectedCavi,
          cantiereId: 1 // TODO: Get from context
        })
      })

      const result = await response.json()
      if (result.success) {
        alert(result.message)
        // TODO: Refresh data
      } else {
        alert(`Errore: ${result.error}`)
      }
    } catch (error) {
      alert('Errore durante l\'eliminazione')
    }
  }

  // Define columns matching original webapp structure
  const columns: ColumnDef[] = useMemo(() => {
    const baseColumns: ColumnDef[] = [
      {
        field: 'id_cavo',
        headerName: 'ID',
        dataType: 'text',
        width: 70,
        align: 'left',
        renderCell: (row: Cavo) => (
          <span className="font-semibold text-mariner-900">{row.id_cavo}</span>
        )
      },
      {
        field: 'sistema',
        headerName: 'Sistema',
        dataType: 'text',
        width: 80,
        renderCell: (row: Cavo) => (
          <TruncatedText text={row.sistema || ''} maxLength={8} />
        )
      },
      {
        field: 'utility',
        headerName: 'Utility',
        dataType: 'text',
        width: 80,
        renderCell: (row: Cavo) => (
          <TruncatedText text={row.utility || ''} maxLength={8} />
        )
      },
      {
        field: 'tipologia',
        headerName: 'Tipologia',
        dataType: 'text',
        width: 100,
        renderCell: (row: Cavo) => (
          <TruncatedText text={row.tipologia || ''} maxLength={12} />
        )
      },
      {
        field: 'formazione',
        headerName: 'Form.',
        dataType: 'text',
        align: 'left',
        width: 60,
        renderCell: (row: Cavo) => row.formazione || row.sezione
      },
      {
        field: 'metri_teorici',
        headerName: 'M.Teor.',
        dataType: 'number',
        align: 'left',
        width: 70,
        renderCell: (row: Cavo) => row.metri_teorici ? row.metri_teorici.toFixed(1) : '0'
      },
      {
        field: 'metri_posati',
        headerName: 'M.Reali',
        dataType: 'number',
        align: 'left',
        width: 70,
        renderCell: (row: Cavo) => {
          const metri = row.metri_posati || row.metratura_reale || 0
          return metri ? metri.toFixed(1) : '0'
        }
      },
      {
        field: 'ubicazione_partenza',
        headerName: 'Da',
        dataType: 'text',
        width: 140,
        renderCell: (row: Cavo) => (
          <TruncatedText
            text={row.da || row.ubicazione_partenza || ''}
            maxLength={18}
          />
        )
      },
      {
        field: 'ubicazione_arrivo',
        headerName: 'A',
        dataType: 'text',
        width: 140,
        renderCell: (row: Cavo) => (
          <TruncatedText
            text={row.a || row.ubicazione_arrivo || ''}
            maxLength={18}
          />
        )
      },
      {
        field: 'id_bobina',
        headerName: 'Bobina',
        dataType: 'text',
        width: 80,
        align: 'center',
        renderCell: (row: Cavo) => {
          const idBobina = row.id_bobina

          // Debug: log del formato bobina per capire il pattern
          if (idBobina && idBobina !== 'BOBINA_VUOTA' && idBobina !== 'N/A') {
          }

          if (!idBobina || idBobina === 'N/A') {
            return <span className="text-gray-400">-</span>
          }

          if (idBobina === 'BOBINA_VUOTA') {
            return (
              <Badge
                variant="outline"
                className="text-xs px-2 py-0.5 text-orange-600 border-orange-300 bg-orange-50"
              >
                Vuota
              </Badge>
            )
          }

          // Estrai solo Y da diversi formati possibili (come nella webapp originale):
          // Pattern dalla webapp originale: /_B(.+)$/
          let match = idBobina.match(/_B(.+)$/)
          if (match) {
            return <span className="font-medium">{match[1]}</span>
          }

          // Pattern alternativo: _b (minuscolo)
          match = idBobina.match(/_b(.+)$/)
          if (match) {
            return <span className="font-medium">{match[1]}</span>
          }

          // Pattern per cX_bY o cX_BY
          match = idBobina.match(/c\d+_[bB](\d+)$/)
          if (match) {
            return <span className="font-medium">{match[1]}</span>
          }

          // Pattern generale per numeri alla fine
          match = idBobina.match(/(\d+)$/)
          if (match) {
            return <span className="font-medium">{match[1]}</span>
          }

          // Se non corrisponde a nessun pattern, mostra l'ID completo
          return <span className="font-medium text-xs">{idBobina}</span>
        }
      },
      {
        field: 'stato_installazione',
        headerName: 'Stato',
        dataType: 'text',
        align: 'left',
        width: 120,
        disableFilter: true,
        disableSort: true,
        renderCell: (row: Cavo) => getStatusButton(row)
      },
      {
        field: 'collegamenti',
        headerName: 'Collegamenti',
        dataType: 'text',
        align: 'left',
        width: 180,
        disableFilter: true,
        disableSort: true,
        renderCell: (row: Cavo) => getConnectionButton(row)
      },
      {
        field: 'certificato',
        headerName: 'Certificato',
        dataType: 'text',
        align: 'left',
        width: 130,
        disableFilter: true,
        disableSort: true,
        renderCell: (row: Cavo) => getCertificationButton(row)
      },

    ]

    // Add selection column if enabled
    if (internalSelectionEnabled) {
      baseColumns.unshift({
        field: 'selection',
        headerName: '',
        disableFilter: true,
        disableSort: true,
        width: 50,
        align: 'left',
        renderHeader: () => (
          <Checkbox
            checked={selectedCavi.length === filteredCavi.length && filteredCavi.length > 0}
            onCheckedChange={handleSelectAll}
          />
        ),
        renderCell: (row: Cavo) => (
          <Checkbox
            checked={selectedCavi.includes(row.id_cavo)}
            onCheckedChange={(checked) => handleSelectCavo(row.id_cavo, checked as boolean)}
            onClick={(e) => e.stopPropagation()}
          />
        )
      })
    }

    return baseColumns
  }, [internalSelectionEnabled, selectedCavi, filteredCavi, handleSelectAll, handleSelectCavo])

  // Custom row renderer for selection and context menu
  const renderRow = (row: Cavo, index: number) => {
    const isSelected = selectedCavi.includes(row.id_cavo)

    return (
      <TableRow
        key={row.id_cavo}
        className={`
          ${isSelected ? 'bg-blue-50 border-blue-200' : 'bg-white'}
          hover:bg-blue-50 hover:border-blue-200 hover:shadow-sm
          cursor-pointer border-b border-gray-200
          transition-all duration-200 ease-in-out
          ${isSelected ? 'ring-1 ring-blue-300' : ''}
        `}
        onClick={() => internalSelectionEnabled && handleSelectCavo(row.id_cavo, !isSelected)}
        onContextMenu={(e) => {
          e.preventDefault()
          onContextMenuAction?.(row, 'context_menu')
        }}
      >
        {columns.map((column) => (
          <TableCell
            key={column.field}
            className={`
              py-2 px-2 text-sm text-left
              ${isSelected ? 'text-blue-900' : 'text-gray-900'}
              transition-colors duration-200
            `}
            style={{ width: column.width, ...column.cellStyle }}
            onClick={(e) => {
              // Prevent row click for action columns
              if (['stato_installazione', 'collegamenti', 'certificato'].includes(column.field)) {
                e.stopPropagation()
              }
            }}
          >
            {column.renderCell ? column.renderCell(row) : (row[column.field] || <span className="text-gray-400">-</span>)}
          </TableCell>
        ))}
      </TableRow>
    )
  }

  // Funzioni di utilità per lo stato
  const getStatusBadge = (cavo: Cavo) => {
    // Verifica se il cavo è assegnato a una comanda
    const comandaPosa = cavo.comanda_posa
    const comandaPartenza = cavo.comanda_partenza
    const comandaArrivo = cavo.comanda_arrivo
    const comandaCertificazione = cavo.comanda_certificazione

    // Trova la comanda attiva (priorità: posa > partenza > arrivo > certificazione)
    const comandaAttiva = comandaPosa || comandaPartenza || comandaArrivo || comandaCertificazione

    // Se c'è una comanda attiva e lo stato è "In corso", mostra il codice comanda
    if (comandaAttiva && cavo.stato_installazione === 'In corso') {
      const colorClasses = getCavoColorClasses('IN_CORSO')
      return (
        <Badge
          className={`cursor-pointer ${colorClasses.badge} ${colorClasses.hover}`}
          onClick={() => onStatusAction?.(cavo, 'view_command', comandaAttiva)}
        >
          {comandaAttiva}
        </Badge>
      )
    }

    // Logica normale per gli altri stati
    const stato = cavo.stato_installazione || 'Da installare'
    const colorClasses = getCavoColorClasses(stato)

    return (
      <Badge className={colorClasses.badge}>
        {stato}
      </Badge>
    )
  }

  const getStatusButton = (cavo: Cavo) => {
    // Verifica se il cavo è installato controllando metri_posati o metratura_reale
    const metriInstallati = cavo.metri_posati || cavo.metratura_reale || 0
    const isInstalled = metriInstallati > 0

    // Verifica se il cavo è assegnato a una comanda attiva
    const comandaPosa = cavo.comanda_posa
    const comandaPartenza = cavo.comanda_partenza
    const comandaArrivo = cavo.comanda_arrivo
    const comandaCertificazione = cavo.comanda_certificazione
    const comandaAttiva = comandaPosa || comandaPartenza || comandaArrivo || comandaCertificazione

    // Se c'è una comanda attiva e lo stato è "In corso", mostra il codice comanda
    if (comandaAttiva && cavo.stato_installazione === 'In corso') {
      return (
        <ActionTooltip action="install" cableId={cavo.id_cavo}>
          <Badge
            className="bg-blue-600 text-white cursor-pointer hover:bg-blue-700 hover:scale-105 transition-all duration-200 hover:shadow-md px-3 py-1 font-semibold"
            onClick={(e) => {
              e.stopPropagation()
              onStatusAction?.(cavo, 'view_command', comandaAttiva)
            }}
          >
            <Settings className="w-3 h-3 mr-1" />
            {comandaAttiva}
          </Badge>
        </ActionTooltip>
      )
    }

    // Determina lo stato del cavo
    const stato = cavo.stato_installazione || 'Da installare'

    if (stato === 'Installato' || isInstalled) {
      return (
        <ActionTooltip action="modify" cableId={cavo.id_cavo}>
          <Badge
            className="bg-green-100 text-green-800 cursor-pointer hover:bg-green-200 hover:text-green-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-green-300"
            onClick={(e) => {
              e.stopPropagation()
              onStatusAction?.(cavo, 'modify_reel')
            }}
          >
            <CheckCircle className="w-3 h-3 mr-1" />
            Installato
          </Badge>
        </ActionTooltip>
      )
    } else if (stato === 'In corso') {
      return (
        <ActionTooltip action="install" cableId={cavo.id_cavo}>
          <Badge
            className="bg-yellow-100 text-yellow-800 cursor-pointer hover:bg-yellow-200 hover:text-yellow-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-yellow-300"
            onClick={(e) => {
              e.stopPropagation()
              onStatusAction?.(cavo, 'insert_meters')
            }}
          >
            <Play className="w-3 h-3 mr-1" />
            In corso
          </Badge>
        </ActionTooltip>
      )
    } else {
      return (
        <ActionTooltip action="install" cableId={cavo.id_cavo}>
          <Badge
            variant="outline"
            className="text-gray-700 cursor-pointer hover:bg-blue-50 hover:text-blue-700 hover:border-blue-300 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium"
            onClick={(e) => {
              e.stopPropagation()
              onStatusAction?.(cavo, 'insert_meters')
            }}
          >
            <Pause className="w-3 h-3 mr-1" />
            Da installare
          </Badge>
        </ActionTooltip>
      )
    }
  }

  const getConnectionButton = (cavo: Cavo) => {
    const isInstalled = cavo.metri_posati > 0 || cavo.metratura_reale > 0
    const collegamento = cavo.collegamento || cavo.collegamenti || 0

    if (!isInstalled) {
      return (
        <ActionTooltip action="connect" disabled={true}>
          <Badge
            variant="outline"
            className="text-gray-400 cursor-not-allowed bg-gray-50 border-gray-200 px-3 py-1"
          >
            <X className="w-3 h-3 mr-1" />
            Non disponibile
          </Badge>
        </ActionTooltip>
      )
    }

    let label, actionType, badgeClass, icon, tooltipAction

    switch (collegamento) {
      case 0:
        label = "Collega"
        actionType = "connect_cable"
        badgeClass = "bg-gray-100 text-gray-700 cursor-pointer hover:bg-blue-50 hover:text-blue-700 hover:border-blue-300 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-gray-300"
        icon = <Link className="w-3 h-3 mr-1" />
        tooltipAction = "connect"
        break
      case 1:
        label = "Completa Arrivo"
        actionType = "connect_arrival"
        badgeClass = "bg-yellow-100 text-yellow-800 cursor-pointer hover:bg-yellow-200 hover:text-yellow-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-yellow-300"
        icon = <Link className="w-3 h-3 mr-1" />
        tooltipAction = "connect"
        break
      case 2:
        label = "Completa Partenza"
        actionType = "connect_departure"
        badgeClass = "bg-yellow-100 text-yellow-800 cursor-pointer hover:bg-yellow-200 hover:text-yellow-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-yellow-300"
        icon = <Link className="w-3 h-3 mr-1" />
        tooltipAction = "connect"
        break
      case 3:
        label = "Scollega"
        actionType = "disconnect_cable"
        badgeClass = "bg-green-100 text-green-800 cursor-pointer hover:bg-green-200 hover:text-green-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-green-300"
        icon = <Unlink className="w-3 h-3 mr-1" />
        tooltipAction = "disconnect"
        break
      default:
        label = "Gestisci"
        actionType = "manage_connections"
        badgeClass = "bg-blue-100 text-blue-800 cursor-pointer hover:bg-blue-200 hover:text-blue-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-blue-300"
        icon = <Settings className="w-3 h-3 mr-1" />
        tooltipAction = "connect"
        break
    }

    return (
      <ActionTooltip action={tooltipAction} cableId={cavo.id_cavo}>
        <Badge
          className={badgeClass}
          onClick={(e) => {
            e.stopPropagation()
            onStatusAction?.(cavo, actionType)
          }}
        >
          {icon}
          {label}
        </Badge>
      </ActionTooltip>
    )
  }

  const getCertificationButton = (cavo: Cavo) => {
    const isInstalled = cavo.metri_posati > 0 || cavo.metratura_reale > 0
    const isCertified = cavo.certificato === true || cavo.certificato === 'SI' || cavo.certificato === 'CERTIFICATO'

    if (!isInstalled) {
      return (
        <ActionTooltip action="certify" disabled={true}>
          <Badge
            variant="outline"
            className="text-gray-400 cursor-not-allowed bg-gray-50 border-gray-200 px-3 py-1"
          >
            <X className="w-3 h-3 mr-1" />
            Non disponibile
          </Badge>
        </ActionTooltip>
      )
    }

    if (isCertified) {
      return (
        <ActionTooltip action="generate_pdf" cableId={cavo.id_cavo}>
          <Badge
            className="bg-green-600 text-white cursor-pointer hover:bg-green-700 transition-all duration-200 hover:scale-105 px-3 py-1 font-semibold"
            onClick={(e) => {
              e.stopPropagation()
              onStatusAction?.(cavo, 'generate_pdf')
            }}
          >
            <Download className="w-3 h-3 mr-1" />
            Genera PDF
          </Badge>
        </ActionTooltip>
      )
    }

    return (
      <ActionTooltip action="certify" cableId={cavo.id_cavo}>
        <Badge
          variant="outline"
          className="text-purple-700 cursor-pointer hover:bg-purple-50 hover:text-purple-800 hover:border-purple-300 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border-purple-200"
          onClick={(e) => {
            e.stopPropagation()
            onStatusAction?.(cavo, 'create_certificate')
          }}
        >
          <Award className="w-3 h-3 mr-1" />
          Certifica
        </Badge>
      </ActionTooltip>
    )
  }

  return (
    <div className="relative">
      {/* Smart Filter */}
      <SmartCaviFilter
        cavi={cavi}
        onFilteredDataChange={handleSmartFilterChange}
        loading={loading}
        selectionEnabled={internalSelectionEnabled}
        onSelectionToggle={handleSelectionToggle}
      />

      {/* Filterable Table */}
      <FilterableTable
        data={smartFilteredCavi}
        columns={columns}
        loading={loading}
        emptyMessage="Nessun cavo disponibile"
        onFilteredDataChange={handleTableFilterChange}
        renderRow={renderRow}
      />

      {/* Contextual Action Bar - appears only when items are selected */}
      {internalSelectionEnabled && selectedCavi.length > 0 && (
        <div className="sticky bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-10">
          <div className="flex items-center justify-between p-3">
            <div className="flex items-center space-x-3">
              <Badge variant="secondary" className="bg-mariner-100 text-mariner-800">
                {selectedCavi.length} cavi selezionati
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleSelectAll(false)}
                className="text-xs"
              >
                Deseleziona tutto
              </Button>
            </div>

            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleBulkExport()}
                className="flex items-center space-x-1"
              >
                <span>📊</span>
                <span>Esporta</span>
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={() => handleBulkStatusChange()}
                className="flex items-center space-x-1"
              >
                <span>🔄</span>
                <span>Cambia Stato</span>
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={() => handleBulkAssignCommand()}
                className="flex items-center space-x-1"
              >
                <span>📋</span>
                <span>Assegna Comanda</span>
              </Button>

              <Button
                variant="destructive"
                size="sm"
                onClick={() => handleBulkDelete()}
                className="flex items-center space-x-1"
              >
                <span>🗑️</span>
                <span>Elimina</span>
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
