"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5454],{15452:(e,t,n)=>{n.d(t,{UC:()=>en,VY:()=>eo,ZL:()=>ee,bL:()=>X,bm:()=>ea,hE:()=>er,hJ:()=>et,l9:()=>$});var r=n(12115),o=n(85185),a=n(6101),l=n(46081),i=n(61285),s=n(5845),d=n(19178),u=n(25519),c=n(34378),p=n(28905),f=n(63655),h=n(92293),g=n(93795),y=n(38168),v=n(99708),m=n(95155),k="Dialog",[x,D]=(0,l.A)(k),[b,w]=x(k),j=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:a,onOpenChange:l,modal:d=!0}=e,u=r.useRef(null),c=r.useRef(null),[p,f]=(0,s.i)({prop:o,defaultProp:null!=a&&a,onChange:l,caller:k});return(0,m.jsx)(b,{scope:t,triggerRef:u,contentRef:c,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:f,onOpenToggle:r.useCallback(()=>f(e=>!e),[f]),modal:d,children:n})};j.displayName=k;var C="DialogTrigger",A=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=w(C,n),i=(0,a.s)(t,l.triggerRef);return(0,m.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":S(l.open),...r,ref:i,onClick:(0,o.m)(e.onClick,l.onOpenToggle)})});A.displayName=C;var M="DialogPortal",[R,I]=x(M,{forceMount:void 0}),N=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:a}=e,l=w(M,t);return(0,m.jsx)(R,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,m.jsx)(p.C,{present:n||l.open,children:(0,m.jsx)(c.Z,{asChild:!0,container:a,children:e})}))})};N.displayName=M;var O="DialogOverlay",_=r.forwardRef((e,t)=>{let n=I(O,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=w(O,e.__scopeDialog);return a.modal?(0,m.jsx)(p.C,{present:r||a.open,children:(0,m.jsx)(F,{...o,ref:t})}):null});_.displayName=O;var E=(0,v.TL)("DialogOverlay.RemoveScroll"),F=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=w(O,n);return(0,m.jsx)(g.A,{as:E,allowPinchZoom:!0,shards:[o.contentRef],children:(0,m.jsx)(f.sG.div,{"data-state":S(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),q="DialogContent",P=r.forwardRef((e,t)=>{let n=I(q,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=w(q,e.__scopeDialog);return(0,m.jsx)(p.C,{present:r||a.open,children:a.modal?(0,m.jsx)(V,{...o,ref:t}):(0,m.jsx)(T,{...o,ref:t})})});P.displayName=q;var V=r.forwardRef((e,t)=>{let n=w(q,e.__scopeDialog),l=r.useRef(null),i=(0,a.s)(t,n.contentRef,l);return r.useEffect(()=>{let e=l.current;if(e)return(0,y.Eq)(e)},[]),(0,m.jsx)(z,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),T=r.forwardRef((e,t)=>{let n=w(q,e.__scopeDialog),o=r.useRef(!1),a=r.useRef(!1);return(0,m.jsx)(z,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,l;null==(r=e.onCloseAutoFocus)||r.call(e,t),t.defaultPrevented||(o.current||null==(l=n.triggerRef.current)||l.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var r,l;null==(r=e.onInteractOutside)||r.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let i=t.target;(null==(l=n.triggerRef.current)?void 0:l.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),z=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:i,...s}=e,c=w(q,n),p=r.useRef(null),f=(0,a.s)(t,p);return(0,h.Oh)(),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(u.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,m.jsx)(d.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":S(c.open),...s,ref:f,onDismiss:()=>c.onOpenChange(!1)})}),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(Y,{titleId:c.titleId}),(0,m.jsx)(Q,{contentRef:p,descriptionId:c.descriptionId})]})]})}),B="DialogTitle",G=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=w(B,n);return(0,m.jsx)(f.sG.h2,{id:o.titleId,...r,ref:t})});G.displayName=B;var Z="DialogDescription",H=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=w(Z,n);return(0,m.jsx)(f.sG.p,{id:o.descriptionId,...r,ref:t})});H.displayName=Z;var W="DialogClose",L=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=w(W,n);return(0,m.jsx)(f.sG.button,{type:"button",...r,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function S(e){return e?"open":"closed"}L.displayName=W;var U="DialogTitleWarning",[J,K]=(0,l.q)(U,{contentName:q,titleName:B,docsSlug:"dialog"}),Y=e=>{let{titleId:t}=e,n=K(U),o="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&(document.getElementById(t)||console.error(o))},[o,t]),null},Q=e=>{let{contentRef:t,descriptionId:n}=e,o=K("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");n&&r&&(document.getElementById(n)||console.warn(a))},[a,t,n]),null},X=j,$=A,ee=N,et=_,en=P,er=G,eo=H,ea=L},23837:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("file-down",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M12 18v-6",key:"17g6i2"}],["path",{d:"m9 15 3 3 3-3",key:"1npd3o"}]])},35169:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},37108:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},47957:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("construction",[["rect",{x:"2",y:"6",width:"20",height:"8",rx:"1",key:"1estib"}],["path",{d:"M17 14v7",key:"7m2elx"}],["path",{d:"M7 14v7",key:"1cm7wv"}],["path",{d:"M17 3v3",key:"1v4jwn"}],["path",{d:"M7 3v3",key:"7o6guu"}],["path",{d:"M10 14 2.3 6.3",key:"1023jk"}],["path",{d:"m14 6 7.7 7.7",key:"1s8pl2"}],["path",{d:"m8 6 8 8",key:"hl96qh"}]])},72713:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},83744:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("file-up",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M12 12v6",key:"3ahymv"}],["path",{d:"m15 15-3-3-3 3",key:"15xj92"}]])}}]);