'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { DangerButton, SecondaryButton } from '@/components/ui/animated-button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { usersApi } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'
import { Loader2, RotateCcw, AlertTriangle, Trash2, Shield, Clock, Eye, EyeOff } from 'lucide-react'

export default function ResetDatabase() {
  const { user } = useAuth()
  const [confirmText, setConfirmText] = useState('')
  const [confirmChecked, setConfirmChecked] = useState(false)
  const [adminPassword, setAdminPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [countdown, setCountdown] = useState(10)
  const [countdownActive, setCountdownActive] = useState(false)
  const [finalConfirmation, setFinalConfirmation] = useState(false)

  // Timer di countdown per la conferma finale
  useEffect(() => {
    let interval: NodeJS.Timeout
    if (countdownActive && countdown > 0) {
      interval = setInterval(() => {
        setCountdown(prev => prev - 1)
      }, 1000)
    } else if (countdown === 0) {
      setFinalConfirmation(true)
      setCountdownActive(false)
    }
    return () => clearInterval(interval)
  }, [countdownActive, countdown])

  const startCountdown = () => {
    if (confirmText === 'RESET DATABASE' && confirmChecked && adminPassword.trim()) {
      setCountdownActive(true)
      setCountdown(10)
      setFinalConfirmation(false)
      setError('')
    } else {
      setError('Completa tutti i passaggi di conferma prima di procedere')
    }
  }

  const handleReset = async () => {
    if (!finalConfirmation) {
      setError('Devi completare il countdown di sicurezza')
      return
    }

    setLoading(true)
    setError('')
    setSuccess('')

    try {
      // Verifica password admin (simulata - in produzione verificare con il backend)
      if (!adminPassword.trim()) {
        throw new Error('Password amministratore richiesta')
      }

      await usersApi.resetDatabase()
      setSuccess('Database resettato con successo! Tutti i dati sono stati eliminati.')

      // Reset form
      setConfirmText('')
      setConfirmChecked(false)
      setAdminPassword('')
      setFinalConfirmation(false)
      setCountdownActive(false)
      setCountdown(10)
    } catch (err: any) {
      setError(err.response?.data?.detail || err.message || 'Errore durante il reset del database')
    } finally {
      setLoading(false)
    }
  }

  const isPreResetEnabled = confirmText === 'RESET DATABASE' && confirmChecked && adminPassword.trim() && !loading && !countdownActive
  const isResetEnabled = finalConfirmation && !loading

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-red-600">
          <RotateCcw className="h-5 w-5" />
          Reset Database
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Avviso di pericolo */}
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <AlertTriangle className="h-6 w-6 text-red-600 mt-0.5" />
            <div>
              <h4 className="font-bold text-red-900 text-lg">⚠️ ATTENZIONE - OPERAZIONE IRREVERSIBILE</h4>
              <div className="text-red-700 mt-2 space-y-2">
                <p className="font-medium">
                  Questa operazione eliminerà PERMANENTEMENTE tutti i dati dal database:
                </p>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>Tutti gli utenti (eccetto l'amministratore principale)</li>
                  <li>Tutti i cantieri e i progetti</li>
                  <li>Tutti i cavi installati</li>
                  <li>Tutte le bobine del parco cavi</li>
                  <li>Tutti i comandi e le certificazioni</li>
                  <li>Tutti i report e i dati di produttività</li>
                </ul>
                <p className="font-bold text-red-800 mt-3">
                  NON È POSSIBILE RECUPERARE I DATI DOPO IL RESET!
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Messaggi di stato */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-600">{error}</p>
          </div>
        )}

        {success && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <p className="text-green-600">{success}</p>
          </div>
        )}

        {/* Form di conferma */}
        <div className="space-y-4 border-t pt-6">
          <div>
            <h4 className="font-semibold text-slate-900 mb-4">
              Conferma Reset Database
            </h4>
            <p className="text-sm text-slate-600 mb-4">
              Per procedere con il reset, devi confermare l'operazione seguendo questi passaggi:
            </p>
          </div>

          <div className="space-y-6">
            {/* Step 1: Digitare testo di conferma */}
            <div className="space-y-2">
              <Label htmlFor="confirm-text" className="text-sm font-medium">
                1. Digita esattamente: <code className="bg-slate-100 px-2 py-1 rounded text-red-600 font-bold">RESET DATABASE</code>
              </Label>
              <Input
                id="confirm-text"
                value={confirmText}
                onChange={(e) => setConfirmText(e.target.value)}
                placeholder="Digita: RESET DATABASE"
                disabled={loading || countdownActive}
                className={confirmText === 'RESET DATABASE' ? 'border-green-500' : ''}
              />
            </div>

            {/* Step 2: Checkbox di conferma */}
            <div className="flex items-start space-x-3">
              <Checkbox
                id="confirm-checkbox"
                checked={confirmChecked}
                onCheckedChange={setConfirmChecked}
                disabled={loading || countdownActive}
              />
              <Label htmlFor="confirm-checkbox" className="text-sm leading-relaxed">
                2. Confermo di aver compreso che questa operazione eliminerà TUTTI i dati dal database
                in modo PERMANENTE e IRREVERSIBILE. Ho effettuato un backup se necessario.
              </Label>
            </div>

            {/* Step 3: Password amministratore */}
            <div className="space-y-2">
              <Label htmlFor="admin-password" className="text-sm font-medium flex items-center gap-2">
                <Shield className="h-4 w-4 text-blue-600" />
                3. Inserisci la tua password di amministratore per confermare l'identità
              </Label>
              <div className="relative">
                <Input
                  id="admin-password"
                  type={showPassword ? 'text' : 'password'}
                  value={adminPassword}
                  onChange={(e) => setAdminPassword(e.target.value)}
                  placeholder="Password amministratore"
                  disabled={loading || countdownActive}
                  className={adminPassword.trim() ? 'border-green-500' : ''}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={loading || countdownActive}
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>
          </div>

          {/* Stato di conferma */}
          <div className="bg-slate-50 border border-slate-200 rounded-lg p-4">
            <h5 className="font-medium text-slate-900 mb-3">Stato Conferma:</h5>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <div className={`w-3 h-3 rounded-full ${
                  confirmText === 'RESET DATABASE' ? 'bg-green-500' : 'bg-red-500'
                }`}></div>
                <span>Testo di conferma: {confirmText === 'RESET DATABASE' ? '✓ Corretto' : '✗ Richiesto'}</span>
              </div>
              <div className="flex items-center gap-2">
                <div className={`w-3 h-3 rounded-full ${
                  confirmChecked ? 'bg-green-500' : 'bg-red-500'
                }`}></div>
                <span>Checkbox confermata: {confirmChecked ? '✓ Sì' : '✗ Richiesta'}</span>
              </div>
              <div className="flex items-center gap-2">
                <div className={`w-3 h-3 rounded-full ${
                  adminPassword.trim() ? 'bg-green-500' : 'bg-red-500'
                }`}></div>
                <span>Password amministratore: {adminPassword.trim() ? '✓ Inserita' : '✗ Richiesta'}</span>
              </div>
              {countdownActive && (
                <div className="flex items-center gap-2 mt-3 p-2 bg-orange-50 border border-orange-200 rounded">
                  <Clock className="h-4 w-4 text-orange-600" />
                  <span className="text-orange-700 font-medium">
                    Countdown di sicurezza: {countdown} secondi
                  </span>
                </div>
              )}
              {finalConfirmation && (
                <div className="flex items-center gap-2 mt-3 p-2 bg-red-50 border border-red-200 rounded">
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                  <span className="text-red-700 font-medium">
                    ⚠️ Pronto per il reset - Ultima possibilità di annullare
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Pulsanti di azione */}
          <div className="space-y-3">
            {!finalConfirmation && !countdownActive && (
              <DangerButton
                onClick={startCountdown}
                disabled={!isPreResetEnabled}
                className="w-full"
                size="lg"
                icon={<Clock className="h-5 w-5" />}
              >
                INIZIA COUNTDOWN DI SICUREZZA (10 secondi)
              </DangerButton>
            )}

            {countdownActive && (
              <div className="w-full p-4 bg-orange-50 border-2 border-orange-300 rounded-lg text-center">
                <div className="flex items-center justify-center gap-3">
                  <Clock className="h-6 w-6 text-orange-600 animate-pulse" />
                  <span className="text-lg font-bold text-orange-700">
                    Countdown: {countdown} secondi
                  </span>
                </div>
                <p className="text-sm text-orange-600 mt-2">
                  Il pulsante di reset si attiverà al termine del countdown
                </p>
              </div>
            )}

            {finalConfirmation && (
              <DangerButton
                onClick={handleReset}
                disabled={!isResetEnabled}
                className="w-full animate-pulse"
                size="lg"
                loading={loading}
                icon={<Trash2 className="h-5 w-5" />}
                glow
              >
                {loading ? 'RESET IN CORSO...' : '🚨 RESET DATABASE - ELIMINA TUTTI I DATI 🚨'}
              </DangerButton>
            )}

            {!isPreResetEnabled && !finalConfirmation && !countdownActive && (
              <p className="text-center text-sm text-slate-500">
                Completa tutti i passaggi di conferma per iniziare il countdown
              </p>
            )}

            {finalConfirmation && (
              <SecondaryButton
                onClick={() => {
                  setFinalConfirmation(false)
                  setCountdownActive(false)
                  setCountdown(10)
                }}
                className="w-full"
                size="lg"
                disabled={loading}
              >
                ANNULLA RESET
              </SecondaryButton>
            )}
          </div>
        </div>

        {/* Informazioni aggiuntive */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-sm">
          <h5 className="font-medium text-blue-900 mb-2">Informazioni Tecniche:</h5>
          <ul className="text-blue-700 space-y-1">
            <li>• Il reset manterrà la struttura delle tabelle</li>
            <li>• L'utente amministratore principale verrà ricreato</li>
            <li>• Le configurazioni di sistema verranno ripristinate ai valori di default</li>
            <li>• L'operazione può richiedere alcuni minuti per completarsi</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}
